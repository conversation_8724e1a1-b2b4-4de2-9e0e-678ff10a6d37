version: '3.8'

services:
  # Laravel Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: redo-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
      - ./storage:/var/www/html/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=redo
      - DB_USERNAME=redo_user
      - DB_PASSWORD=redo_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CACHE_STORE=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - redo-network

  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: redo-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    networks:
      - redo-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: redo-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: redo
      MYSQL_USER: redo_user
      MYSQL_PASSWORD: redo_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    networks:
      - redo-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: redo-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - redo-network

  # Node.js for development (Vite dev server)
  node:
    image: node:20-alpine
    container_name: redo-node
    working_dir: /app
    volumes:
      - .:/app
      - node_modules:/app/node_modules
    ports:
      - "5173:5173"
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0"
    networks:
      - redo-network

  # Queue Worker
  queue:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: redo-queue
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
    environment:
      - APP_ENV=local
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=redo
      - DB_USERNAME=redo_user
      - DB_PASSWORD=redo_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - QUEUE_CONNECTION=redis
    command: php artisan queue:work --tries=3 --timeout=90
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - redo-network

  # Scheduler (Laravel Cron)
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: redo-scheduler
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
    environment:
      - APP_ENV=local
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=redo
      - DB_USERNAME=redo_user
      - DB_PASSWORD=redo_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    command: sh -c "while true; do php artisan schedule:run; sleep 60; done"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - redo-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  node_modules:
    driver: local

networks:
  redo-network:
    driver: bridge
